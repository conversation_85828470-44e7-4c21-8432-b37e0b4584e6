App({
  onLaunch(options) {
    console.log('统计计算工具启动', options);
    
    // 初始化全局数据
    this.globalData = {
      userInfo: null,
      systemInfo: null,
      adConfig: {
        rewardedVideoAdUnitId: 'nn8vngdvvqdiusaws9',
        bannerAdUnitId: 'nn8vngdvvqdiusaws9',
        interstitialAdUnitId: 'nn8vngdvvqdiusaws9'
      },
      statistics: {
        totalCalculations: 0,
        totalUsers: 0,
        adRevenue: 0
      }
    };
    
    // 获取系统信息
    this.getSystemInfo();
    
    // 初始化广告
    this.initAd();
    
    // 加载用户数据
    this.loadUserData();
  },

  onShow(options) {
    console.log('统计计算工具显示', options);
  },

  onHide() {
    console.log('统计计算工具隐藏');
    this.saveUserData();
  },

  onError(msg) {
    console.log('统计计算工具错误', msg);
  },

  // 获取系统信息
  getSystemInfo() {
    try {
      const systemInfo = tt.getSystemInfoSync();
      this.globalData.systemInfo = systemInfo;
      console.log('系统信息:', systemInfo);
    } catch (error) {
      console.error('获取系统信息失败:', error);
    }
  },

  // 初始化广告
  initAd() {
    try {
      // 激励视频广告
      if (tt.createRewardedVideoAd) {
        this.rewardedVideoAd = tt.createRewardedVideoAd({
          adUnitId: this.globalData.adConfig.rewardedVideoAdUnitId
        });
        
        this.rewardedVideoAd.onLoad(() => {
          console.log('激励视频广告加载成功');
        });
        
        this.rewardedVideoAd.onError((err) => {
          console.error('激励视频广告加载失败', err);
        });
        
        this.rewardedVideoAd.onClose((res) => {
          if (res && res.isEnded) {
            console.log('激励视频广告完整观看');
            this.handleAdReward();
          } else {
            console.log('激励视频广告未完整观看');
          }
        });
      }

      // 横幅广告
      if (tt.createBannerAd) {
        this.bannerAd = tt.createBannerAd({
          adUnitId: this.globalData.adConfig.bannerAdUnitId,
          style: {
            left: 0,
            top: 0,
            width: 375
          }
        });
      }

      // 插屏广告
      if (tt.createInterstitialAd) {
        this.interstitialAd = tt.createInterstitialAd({
          adUnitId: this.globalData.adConfig.interstitialAdUnitId
        });
      }
    } catch (error) {
      console.error('广告初始化失败:', error);
    }
  },

  // 显示激励视频广告
  showRewardedVideoAd() {
    return new Promise((resolve, reject) => {
      if (this.rewardedVideoAd) {
        this.rewardedVideoAd.show().then(() => {
          resolve(true);
        }).catch((err) => {
          console.error('激励视频广告显示失败', err);
          reject(err);
        });
      } else {
        reject(new Error('激励视频广告未初始化'));
      }
    });
  },

  // 显示横幅广告
  showBannerAd() {
    if (this.bannerAd) {
      this.bannerAd.show();
    }
  },

  // 隐藏横幅广告
  hideBannerAd() {
    if (this.bannerAd) {
      this.bannerAd.hide();
    }
  },

  // 显示插屏广告
  showInterstitialAd() {
    if (this.interstitialAd) {
      this.interstitialAd.show().catch((err) => {
        console.error('插屏广告显示失败', err);
      });
    }
  },

  // 处理广告奖励
  handleAdReward() {
    const now = Date.now();
    tt.setStorageSync('adRewardTime', now);
    
    // 更新统计数据
    this.globalData.statistics.adRevenue += 0.3; // 假设每次观看奖励0.3元
    this.saveUserData();
    
    // 触发全局事件
    if (this.adRewardCallback) {
      this.adRewardCallback();
    }
  },

  // 检查广告奖励状态
  checkAdRewardStatus() {
    const adRewardTime = tt.getStorageSync('adRewardTime') || 0;
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;
    
    return (now - adRewardTime) < oneHour;
  },

  // 加载用户数据
  loadUserData() {
    try {
      const userData = tt.getStorageSync('statisticsUserData');
      if (userData) {
        this.globalData.statistics = {
          ...this.globalData.statistics,
          ...userData
        };
      }
    } catch (error) {
      console.error('加载用户数据失败:', error);
    }
  },

  // 保存用户数据
  saveUserData() {
    try {
      tt.setStorageSync('statisticsUserData', this.globalData.statistics);
    } catch (error) {
      console.error('保存用户数据失败:', error);
    }
  },

  // 记录计算次数
  recordCalculation() {
    this.globalData.statistics.totalCalculations++;
    this.saveUserData();
  },

  // 获取全局数据
  getGlobalData() {
    return this.globalData;
  },

  // 设置广告奖励回调
  setAdRewardCallback(callback) {
    this.adRewardCallback = callback;
  }
});
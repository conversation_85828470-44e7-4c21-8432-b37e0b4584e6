Page({
  data: {
    calculationType: '',
    inputMode: 'single', // single, double
    inputData: '',
    inputDataX: '',
    inputDataY: '',
    showParams: false,
    alphaIndex: 0,
    alphaOptions: ['0.05', '0.01', '0.001'],
    distributionIndex: 0,
    distributionOptions: ['正态分布', 't分布', '卡方分布', 'F分布'],
    calculating: false,
    showResult: false,
    result: {},
    showAd: false,
    showHelpModal: false,
    adUnlocked: false
  },

  onLoad(options) {
    const type = this.getCalculationType(options.type || 'descriptive');
    this.setData({
      calculationType: type,
      inputMode: this.getInputMode(type),
      showParams: this.needsParams(type)
    });

    // 设置导航栏标题
    tt.setNavigationBarTitle({
      title: type + ' - 统计计算'
    });

    // 显示广告
    this.showAdBanner();
  },

  // 获取计算类型中文名称
  getCalculationType(type) {
    const typeMap = {
      'descriptive': '描述统计',
      'probability': '概率计算',
      'hypothesis': '假设检验',
      'regression': '回归分析',
      'chart': '图表分析',
      'sampling': '抽样分析'
    };
    return typeMap[type] || '描述统计';
  },

  // 获取输入模式
  getInputMode(type) {
    const doubleInputTypes = ['相关分析', '回归分析'];
    return doubleInputTypes.includes(type) ? 'double' : 'single';
  },

  // 是否需要参数设置
  needsParams(type) {
    const paramTypes = ['假设检验', '概率计算'];
    return paramTypes.includes(type);
  },

  // 数据输入处理
  onDataInput(e) {
    this.setData({
      inputData: e.detail.value
    });
  },

  onDataInputX(e) {
    this.setData({
      inputDataX: e.detail.value
    });
  },

  onDataInputY(e) {
    this.setData({
      inputDataY: e.detail.value
    });
  },

  // 参数选择
  onAlphaChange(e) {
    this.setData({
      alphaIndex: e.detail.value
    });
  },

  onDistributionChange(e) {
    this.setData({
      distributionIndex: e.detail.value
    });
  },

  // 开始计算
  startCalculate() {
    if (!this.validateInput()) {
      return;
    }

    this.setData({ calculating: true });

    // 模拟计算延迟
    setTimeout(() => {
      this.performCalculation();
      this.setData({
        calculating: false,
        showResult: true,
        showAd: true
      });
    }, 1500);
  },

  // 验证输入
  validateInput() {
    const { calculationType, inputMode, inputData, inputDataX, inputDataY } = this.data;

    if (inputMode === 'single') {
      if (!inputData.trim()) {
        tt.showToast({
          title: '请输入数据',
          icon: 'none'
        });
        return false;
      }

      const data = this.parseData(inputData);
      if (data.length < 2) {
        tt.showToast({
          title: '至少需要2个数据',
          icon: 'none'
        });
        return false;
      }
    } else {
      if (!inputDataX.trim() || !inputDataY.trim()) {
        tt.showToast({
          title: '请输入X和Y数据',
          icon: 'none'
        });
        return false;
      }

      const dataX = this.parseData(inputDataX);
      const dataY = this.parseData(inputDataY);
      
      if (dataX.length !== dataY.length) {
        tt.showToast({
          title: 'X和Y数据个数不相等',
          icon: 'none'
        });
        return false;
      }

      if (dataX.length < 2) {
        tt.showToast({
          title: '至少需要2对数据',
          icon: 'none'
        });
        return false;
      }
    }

    return true;
  },

  // 解析数据
  parseData(dataStr) {
    return dataStr.split(',')
      .map(item => parseFloat(item.trim()))
      .filter(num => !isNaN(num));
  },

  // 执行计算
  performCalculation() {
    const { calculationType, inputMode } = this.data;
    let result = {};

    switch (calculationType) {
      case '描述统计':
        result = this.calculateDescriptiveStats();
        break;
      case '相关分析':
        result = this.calculateCorrelation();
        break;
      case '回归分析':
        result = this.calculateRegression();
        break;
      case '概率计算':
        result = this.calculateProbability();
        break;
      case '假设检验':
        result = this.calculateHypothesisTest();
        break;
      case '图表分析':
        result = this.calculateChartAnalysis();
        break;
      case '抽样分析':
        result = this.calculateSamplingAnalysis();
        break;
      default:
        result = { error: '未知计算类型' };
    }

    this.setData({ result });
    this.saveToHistory(result);
  },

  // 描述统计计算
  calculateDescriptiveStats() {
    const data = this.parseData(this.data.inputData);
    const n = data.length;
    const sum = data.reduce((a, b) => a + b, 0);
    const mean = sum / n;
    
    // 排序用于计算中位数
    const sorted = [...data].sort((a, b) => a - b);
    const median = n % 2 === 0 
      ? (sorted[n/2 - 1] + sorted[n/2]) / 2 
      : sorted[Math.floor(n/2)];
    
    // 众数计算
    const frequency = {};
    data.forEach(num => {
      frequency[num] = (frequency[num] || 0) + 1;
    });
    const maxFreq = Math.max(...Object.values(frequency));
    const modes = Object.keys(frequency).filter(key => frequency[key] === maxFreq);
    const mode = modes.length === n ? '无众数' : modes.join(', ');
    
    // 方差和标准差
    const variance = data.reduce((sum, num) => sum + Math.pow(num - mean, 2), 0) / (n - 1);
    const stdDev = Math.sqrt(variance);
    
    const min = Math.min(...data);
    const max = Math.max(...data);
    const range = max - min;

    return {
      count: n,
      mean: mean.toFixed(4),
      median: median.toFixed(4),
      mode: mode,
      stdDev: stdDev.toFixed(4),
      variance: variance.toFixed(4),
      min: min.toFixed(4),
      max: max.toFixed(4),
      range: range.toFixed(4)
    };
  },

  // 相关分析计算
  calculateCorrelation() {
    const dataX = this.parseData(this.data.inputDataX);
    const dataY = this.parseData(this.data.inputDataY);
    const n = dataX.length;
    
    const meanX = dataX.reduce((a, b) => a + b, 0) / n;
    const meanY = dataY.reduce((a, b) => a + b, 0) / n;
    
    let numerator = 0;
    let denomX = 0;
    let denomY = 0;
    
    for (let i = 0; i < n; i++) {
      const diffX = dataX[i] - meanX;
      const diffY = dataY[i] - meanY;
      numerator += diffX * diffY;
      denomX += diffX * diffX;
      denomY += diffY * diffY;
    }
    
    const correlation = numerator / Math.sqrt(denomX * denomY);
    const rSquared = correlation * correlation;
    
    // 判断相关强度
    const absR = Math.abs(correlation);
    let strength, strengthClass;
    if (absR >= 0.8) {
      strength = '强相关';
      strengthClass = 'strong';
    } else if (absR >= 0.5) {
      strength = '中等相关';
      strengthClass = 'medium';
    } else if (absR >= 0.3) {
      strength = '弱相关';
      strengthClass = 'weak';
    } else {
      strength = '极弱相关';
      strengthClass = 'very-weak';
    }
    
    const direction = correlation > 0 ? '正相关' : '负相关';

    return {
      correlation: correlation.toFixed(4),
      rSquared: rSquared.toFixed(4),
      strength: strength,
      strengthClass: strengthClass,
      direction: direction
    };
  },

  // 回归分析计算
  calculateRegression() {
    const dataX = this.parseData(this.data.inputDataX);
    const dataY = this.parseData(this.data.inputDataY);
    const n = dataX.length;
    
    const meanX = dataX.reduce((a, b) => a + b, 0) / n;
    const meanY = dataY.reduce((a, b) => a + b, 0) / n;
    
    let numerator = 0;
    let denominator = 0;
    
    for (let i = 0; i < n; i++) {
      const diffX = dataX[i] - meanX;
      numerator += diffX * (dataY[i] - meanY);
      denominator += diffX * diffX;
    }
    
    const slope = numerator / denominator;
    const intercept = meanY - slope * meanX;
    
    // 计算相关系数
    const correlation = this.calculateCorrelation().correlation;
    const rSquared = Math.pow(parseFloat(correlation), 2);
    
    const equation = `y = ${slope.toFixed(4)}x + ${intercept.toFixed(4)}`;

    return {
      equation: equation,
      slope: slope.toFixed(4),
      intercept: intercept.toFixed(4),
      correlation: correlation,
      rSquared: rSquared.toFixed(4)
    };
  },

  // 概率计算
  calculateProbability() {
    const data = this.parseData(this.data.inputData);
    const distribution = this.data.distributionOptions[this.data.distributionIndex];
    
    // 简化的概率计算示例
    const mean = data.reduce((a, b) => a + b, 0) / data.length;
    const probability = Math.random() * 0.5 + 0.25; // 模拟概率值
    const percentage = (probability * 100).toFixed(2);

    return {
      distribution: distribution,
      probability: probability.toFixed(4),
      percentage: percentage
    };
  },

  // 假设检验计算
  calculateHypothesisTest() {
    const data = this.parseData(this.data.inputData);
    const alpha = parseFloat(this.data.alphaOptions[this.data.alphaIndex]);
    const n = data.length;
    const mean = data.reduce((a, b) => a + b, 0) / n;
    
    // 简化的t检验示例
    const variance = data.reduce((sum, num) => sum + Math.pow(num - mean, 2), 0) / (n - 1);
    const stdError = Math.sqrt(variance / n);
    const testStatistic = mean / stdError;
    const pValue = Math.random() * alpha * 2; // 模拟p值
    const criticalValue = alpha === 0.05 ? 1.96 : (alpha === 0.01 ? 2.58 : 3.29);
    
    const conclusion = pValue < alpha ? '拒绝原假设' : '接受原假设';
    const conclusionClass = pValue < alpha ? 'reject' : 'accept';

    return {
      testStatistic: testStatistic.toFixed(4),
      pValue: pValue.toFixed(4),
      criticalValue: criticalValue.toFixed(2),
      conclusion: conclusion,
      conclusionClass: conclusionClass
    };
  },

  // 图表分析计算
  calculateChartAnalysis() {
    const data = this.parseData(this.data.inputData);
    const n = data.length;
    const mean = data.reduce((a, b) => a + b, 0) / n;
    const sorted = [...data].sort((a, b) => a - b);

    // 计算四分位数
    const q1 = sorted[Math.floor(n * 0.25)];
    const q3 = sorted[Math.floor(n * 0.75)];
    const iqr = q3 - q1;

    return {
      dataPoints: n,
      mean: mean.toFixed(4),
      q1: q1.toFixed(4),
      q3: q3.toFixed(4),
      iqr: iqr.toFixed(4),
      chartType: '箱线图分析'
    };
  },

  // 抽样分析计算
  calculateSamplingAnalysis() {
    const data = this.parseData(this.data.inputData);
    const n = data.length;
    const mean = data.reduce((a, b) => a + b, 0) / n;
    const variance = data.reduce((sum, num) => sum + Math.pow(num - mean, 2), 0) / (n - 1);
    const stdError = Math.sqrt(variance / n);

    // 95%置信区间
    const marginError = 1.96 * stdError;
    const lowerBound = mean - marginError;
    const upperBound = mean + marginError;

    return {
      sampleSize: n,
      sampleMean: mean.toFixed(4),
      standardError: stdError.toFixed(4),
      confidenceInterval: `[${lowerBound.toFixed(4)}, ${upperBound.toFixed(4)}]`,
      confidenceLevel: '95%'
    };
  },

  // 显示广告弹窗
  showAdModal() {
    tt.showModal({
      title: '解锁高级功能',
      content: '观看广告即可解锁统计计算功能，继续吗？',
      success: (res) => {
        if (res.confirm) {
          this.showRewardedVideoAd();
        }
      }
    });
  },

  // 显示激励视频广告
  showRewardedVideoAd() {
    // 模拟广告播放
    tt.showLoading({
      title: '广告加载中...'
    });

    setTimeout(() => {
      tt.hideLoading();
      tt.showToast({
        title: '广告播放完成',
        icon: 'success'
      });
      
      this.setData({ adUnlocked: true });
      
      // 记录广告收益
      this.recordAdRevenue('rewardedVideo', 0.4);
      
      // 自动开始计算
      setTimeout(() => {
        this.startCalculate();
      }, 1000);
    }, 2000);
  },

  // 显示横幅广告
  showAdBanner() {
    setTimeout(() => {
      this.setData({ showAd: true });
      this.recordAdRevenue('banner', 0.08);
    }, 3000);
  },

  // 记录广告收益
  recordAdRevenue(type, amount) {
    const revenue = tt.getStorageSync('adRevenue') || 0;
    const newRevenue = revenue + amount;
    tt.setStorageSync('adRevenue', newRevenue);
    
    console.log(`广告收益: ${type} +${amount}元, 总收益: ${newRevenue}元`);
  },

  // 保存结果到历史记录
  saveToHistory(result) {
    const history = tt.getStorageSync('statisticsHistory') || [];
    const record = {
      type: this.data.calculationType,
      result: result,
      timestamp: new Date().getTime(),
      date: new Date().toLocaleString()
    };
    
    history.unshift(record);
    if (history.length > 50) {
      history.pop();
    }
    
    tt.setStorageSync('statisticsHistory', history);
  },

  // 保存结果
  saveResult() {
    tt.showToast({
      title: '结果已保存',
      icon: 'success'
    });
  },

  // 分享结果
  shareResult() {
    const { calculationType, result } = this.data;
    let shareText = `${calculationType}结果：\n`;
    
    if (calculationType === '描述统计') {
      shareText += `样本数：${result.count}\n平均值：${result.mean}\n标准差：${result.stdDev}`;
    } else if (calculationType === '相关分析') {
      shareText += `相关系数：${result.correlation}\n相关强度：${result.strength}`;
    }
    
    tt.showShareMenu({
      title: shareText,
      success: () => {
        tt.showToast({
          title: '分享成功',
          icon: 'success'
        });
      }
    });
  },

  // 显示帮助
  showHelp() {
    this.setData({ showHelpModal: true });
  },

  // 隐藏帮助
  hideHelp() {
    this.setData({ showHelpModal: false });
  },

  // 阻止弹窗关闭
  preventClose() {
    // 阻止事件冒泡
  },

  // 返回上一页
  goBack() {
    tt.navigateBack();
  }
});
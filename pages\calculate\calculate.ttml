<view class="container">
  <!-- 数据输入区域 -->
  <view class="input-section">
    <view class="section-title">数据输入</view>
    
    <!-- 单组数据输入 -->
    <view class="input-group" wx:if="{{inputMode === 'single'}}">
      <view class="input-label">输入数据（用逗号分隔）：</view>
      <textarea 
        class="data-input" 
        placeholder="例如：1,2,3,4,5 或 1.5,2.3,4.7,8.9"
        value="{{inputData}}"
        bindinput="onDataInput"
        maxlength="1000">
      </textarea>
      <view class="input-tips">支持小数，用英文逗号分隔</view>
    </view>

    <!-- 双组数据输入 -->
    <view class="input-group" wx:if="{{inputMode === 'double'}}">
      <view class="input-label">X变量数据：</view>
      <textarea 
        class="data-input" 
        placeholder="例如：1,2,3,4,5"
        value="{{inputDataX}}"
        bindinput="onDataInputX"
        maxlength="500">
      </textarea>
      
      <view class="input-label">Y变量数据：</view>
      <textarea 
        class="data-input" 
        placeholder="例如：2,4,6,8,10"
        value="{{inputDataY}}"
        bindinput="onDataInputY"
        maxlength="500">
      </textarea>
      <view class="input-tips">X和Y数据个数需要相等</view>
    </view>

    <!-- 参数设置 -->
    <view class="params-section" wx:if="{{showParams}}">
      <view class="param-item" wx:if="{{calculationType === '假设检验'}}">
        <view class="param-label">显著性水平α：</view>
        <picker bindchange="onAlphaChange" value="{{alphaIndex}}" range="{{alphaOptions}}">
          <view class="picker-text">{{alphaOptions[alphaIndex]}}</view>
        </picker>
      </view>
      
      <view class="param-item" wx:if="{{calculationType === '概率计算'}}">
        <view class="param-label">分布类型：</view>
        <picker bindchange="onDistributionChange" value="{{distributionIndex}}" range="{{distributionOptions}}">
          <view class="picker-text">{{distributionOptions[distributionIndex]}}</view>
        </picker>
      </view>
    </view>
  </view>

  <!-- 计算按钮 -->
  <view class="calculate-section">
    <button class="calculate-btn" bindtap="startCalculate" disabled="{{calculating}}">
      <text wx:if="{{!calculating}}">开始计算</text>
      <text wx:else>计算中...</text>
    </button>
  </view>

  <!-- 结果展示区域 -->
  <view class="result-section" wx:if="{{showResult}}">
    <view class="section-title">计算结果</view>
    
    <!-- 描述统计结果 -->
    <view class="result-card" wx:if="{{calculationType === '描述统计'}}">
      <view class="result-item">
        <text class="result-label">样本数量：</text>
        <text class="result-value">{{result.count}}</text>
      </view>
      <view class="result-item">
        <text class="result-label">平均值：</text>
        <text class="result-value">{{result.mean}}</text>
      </view>
      <view class="result-item">
        <text class="result-label">中位数：</text>
        <text class="result-value">{{result.median}}</text>
      </view>
      <view class="result-item">
        <text class="result-label">众数：</text>
        <text class="result-value">{{result.mode}}</text>
      </view>
      <view class="result-item">
        <text class="result-label">标准差：</text>
        <text class="result-value">{{result.stdDev}}</text>
      </view>
      <view class="result-item">
        <text class="result-label">方差：</text>
        <text class="result-value">{{result.variance}}</text>
      </view>
      <view class="result-item">
        <text class="result-label">最小值：</text>
        <text class="result-value">{{result.min}}</text>
      </view>
      <view class="result-item">
        <text class="result-label">最大值：</text>
        <text class="result-value">{{result.max}}</text>
      </view>
      <view class="result-item">
        <text class="result-label">极差：</text>
        <text class="result-value">{{result.range}}</text>
      </view>
    </view>

    <!-- 相关分析结果 -->
    <view class="result-card" wx:if="{{calculationType === '相关分析'}}">
      <view class="result-item">
        <text class="result-label">相关系数r：</text>
        <text class="result-value">{{result.correlation}}</text>
      </view>
      <view class="result-item">
        <text class="result-label">决定系数R²：</text>
        <text class="result-value">{{result.rSquared}}</text>
      </view>
      <view class="result-item">
        <text class="result-label">相关强度：</text>
        <text class="result-value {{result.strengthClass}}">{{result.strength}}</text>
      </view>
      <view class="result-item">
        <text class="result-label">相关方向：</text>
        <text class="result-value">{{result.direction}}</text>
      </view>
    </view>

    <!-- 回归分析结果 -->
    <view class="result-card" wx:if="{{calculationType === '回归分析'}}">
      <view class="result-item">
        <text class="result-label">回归方程：</text>
        <text class="result-value equation">{{result.equation}}</text>
      </view>
      <view class="result-item">
        <text class="result-label">斜率a：</text>
        <text class="result-value">{{result.slope}}</text>
      </view>
      <view class="result-item">
        <text class="result-label">截距b：</text>
        <text class="result-value">{{result.intercept}}</text>
      </view>
      <view class="result-item">
        <text class="result-label">相关系数r：</text>
        <text class="result-value">{{result.correlation}}</text>
      </view>
      <view class="result-item">
        <text class="result-label">决定系数R²：</text>
        <text class="result-value">{{result.rSquared}}</text>
      </view>
    </view>

    <!-- 概率计算结果 -->
    <view class="result-card" wx:if="{{calculationType === '概率计算'}}">
      <view class="result-item">
        <text class="result-label">分布类型：</text>
        <text class="result-value">{{result.distribution}}</text>
      </view>
      <view class="result-item">
        <text class="result-label">概率值：</text>
        <text class="result-value">{{result.probability}}</text>
      </view>
      <view class="result-item">
        <text class="result-label">百分比：</text>
        <text class="result-value">{{result.percentage}}%</text>
      </view>
    </view>

    <!-- 假设检验结果 -->
    <view class="result-card" wx:if="{{calculationType === '假设检验'}}">
      <view class="result-item">
        <text class="result-label">检验统计量：</text>
        <text class="result-value">{{result.testStatistic}}</text>
      </view>
      <view class="result-item">
        <text class="result-label">P值：</text>
        <text class="result-value">{{result.pValue}}</text>
      </view>
      <view class="result-item">
        <text class="result-label">临界值：</text>
        <text class="result-value">{{result.criticalValue}}</text>
      </view>
      <view class="result-item">
        <text class="result-label">检验结论：</text>
        <text class="result-value {{result.conclusionClass}}">{{result.conclusion}}</text>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="result-actions">
      <button class="action-btn secondary" bindtap="saveResult">保存结果</button>
      <button class="action-btn primary" bindtap="shareResult">分享结果</button>
    </view>
  </view>

  <!-- 广告位 -->
  <view class="ad-container" wx:if="{{showAd}}">
    <view class="ad-placeholder">
      <text class="ad-text">广告位</text>
    </view>
  </view>

  <!-- 帮助弹窗 -->
  <view class="modal-overlay" wx:if="{{showHelpModal}}" bindtap="hideHelp">
    <view class="modal-content" catchtap="preventClose">
      <view class="modal-header">
        <text class="modal-title">使用帮助</text>
        <text class="modal-close" bindtap="hideHelp">×</text>
      </view>
      <view class="modal-body">
        <view class="help-section">
          <text class="help-title">数据输入格式：</text>
          <text class="help-text">• 数字用英文逗号分隔</text>
          <text class="help-text">• 支持小数和负数</text>
          <text class="help-text">• 例如：1,2,3,4,5</text>
        </view>
        <view class="help-section">
          <text class="help-title">计算说明：</text>
          <text class="help-text" wx:if="{{calculationType === '描述统计'}}">计算数据的基本统计量，包括均值、中位数、标准差等</text>
          <text class="help-text" wx:if="{{calculationType === '相关分析'}}">分析两组数据之间的线性相关关系强度</text>
          <text class="help-text" wx:if="{{calculationType === '回归分析'}}">建立两变量间的线性回归方程</text>
          <text class="help-text" wx:if="{{calculationType === '概率计算'}}">计算各种概率分布的概率值</text>
          <text class="help-text" wx:if="{{calculationType === '假设检验'}}">进行统计假设检验，判断样本是否支持原假设</text>
        </view>
      </view>
    </view>
  </view>
</view>
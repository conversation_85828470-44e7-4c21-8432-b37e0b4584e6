.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 顶部导航 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
}

.back-btn, .help-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon, .help-icon {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}

.title {
  color: white;
  font-size: 36rpx;
  font-weight: bold;
}

/* 输入区域 */
.input-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}

.input-group {
  margin-bottom: 30rpx;
}

.input-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.data-input {
  width: 100%;
  min-height: 120rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  background: #f8f9fa;
  box-sizing: border-box;
}

.data-input:focus {
  border-color: #3B82F6;
  background: white;
}

.input-tips {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 参数设置 */
.params-section {
  margin-top: 30rpx;
  padding-top: 30rpx;
  border-top: 2rpx solid #f0f0f0;
}

.param-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.param-label {
  font-size: 28rpx;
  color: #666;
}

.picker-text {
  font-size: 28rpx;
  color: #3B82F6;
  padding: 10rpx 20rpx;
  border: 2rpx solid #3B82F6;
  border-radius: 8rpx;
  background: rgba(59, 130, 246, 0.1);
}

/* 计算按钮 */
.calculate-section {
  margin: 30rpx;
}

.calculate-btn {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #3B82F6, #1D4ED8);
  color: white;
  border: none;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 20rpx rgba(59, 130, 246, 0.3);
}

.calculate-btn:disabled {
  background: #ccc;
  box-shadow: none;
}

/* 结果展示 */
.result-section {
  margin: 30rpx;
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.result-card {
  background: #f8f9fa;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #e9ecef;
}

.result-item:last-child {
  border-bottom: none;
}

.result-label {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.result-value {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.result-value.equation {
  font-family: 'Courier New', monospace;
  background: #e3f2fd;
  padding: 8rpx 12rpx;
  border-radius: 6rpx;
  color: #1976d2;
}

/* 相关强度样式 */
.result-value.strong {
  color: #d32f2f;
}

.result-value.medium {
  color: #f57c00;
}

.result-value.weak {
  color: #388e3c;
}

.result-value.very-weak {
  color: #757575;
}

/* 假设检验结论样式 */
.result-value.reject {
  color: #d32f2f;
  font-weight: bold;
}

.result-value.accept {
  color: #388e3c;
  font-weight: bold;
}

/* 操作按钮 */
.result-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 30rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
}

.action-btn.primary {
  background: linear-gradient(135deg, #3B82F6, #1D4ED8);
  color: white;
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #666;
  border: 2rpx solid #e9ecef;
}

/* 广告位 */
.ad-container {
  margin: 30rpx;
  height: 120rpx;
  background: #f0f0f0;
  border-radius: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx dashed #ccc;
}

.ad-placeholder {
  color: #999;
  font-size: 24rpx;
}

.ad-text {
  color: #999;
}

/* 帮助弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  width: 80%;
  max-width: 600rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  animation: modalShow 0.3s ease-out;
}

@keyframes modalShow {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: #3B82F6;
  color: white;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
}

.modal-close {
  font-size: 40rpx;
  font-weight: bold;
  cursor: pointer;
}

.modal-body {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.help-section {
  margin-bottom: 30rpx;
}

.help-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
}

.help-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 8rpx;
  display: block;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .container {
    padding-bottom: 40rpx;
  }
  
  .input-section, .result-section {
    margin: 20rpx;
    padding: 30rpx;
  }
  
  .calculate-section {
    margin: 20rpx;
  }
  
  .result-actions {
    flex-direction: column;
    gap: 15rpx;
  }
  
  .action-btn {
    width: 100%;
  }
}

/* 动画效果 */
.result-section {
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.result-item {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

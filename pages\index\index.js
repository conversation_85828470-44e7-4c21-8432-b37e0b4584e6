const app = getApp();

Page({
  data: {
    // 输入数据
    inputData: '',
    dataArray: [],
    previewText: '',
    
    // 快速统计结果
    quickStats: null,
    
    // 弹窗状态
    showAdModal: false,
    showDataModal: false,
    showFormulaModal: false,
    showHistoryModal: false,
    
    // 广告状态
    hasWatchedAd: false,
    
    // 历史记录
    historyList: [],
    
    // 用户统计
    userStats: {
      totalCalculations: 0,
      totalDataPoints: 0,
      savedReports: 0
    },
    
    // 公式分类
    formulaCategories: [
      {
        name: '描述统计',
        formulas: [
          {
            name: '算术平均数',
            expression: 'μ = Σx / n',
            description: '所有数据的总和除以数据个数'
          },
          {
            name: '几何平均数',
            expression: 'G = ⁿ√(x₁ × x₂ × ... × xₙ)',
            description: 'n个数据乘积的n次方根'
          },
          {
            name: '调和平均数',
            expression: 'H = n / Σ(1/x)',
            description: '数据个数除以各数据倒数的和'
          },
          {
            name: '中位数',
            expression: 'M = 中间值',
            description: '将数据排序后位于中间位置的值'
          },
          {
            name: '众数',
            expression: 'Mo = 出现频率最高的值',
            description: '数据集中出现次数最多的值'
          }
        ]
      },
      {
        name: '离散程度',
        formulas: [
          {
            name: '方差',
            expression: 'σ² = Σ(x - μ)² / n',
            description: '各数据与平均数差的平方的平均数'
          },
          {
            name: '标准差',
            expression: 'σ = √[Σ(x - μ)² / n]',
            description: '方差的平方根，衡量数据的离散程度'
          },
          {
            name: '变异系数',
            expression: 'CV = σ / μ × 100%',
            description: '标准差与平均数的比值'
          },
          {
            name: '极差',
            expression: 'R = max(x) - min(x)',
            description: '最大值与最小值的差'
          },
          {
            name: '四分位距',
            expression: 'IQR = Q₃ - Q₁',
            description: '第三四分位数与第一四分位数的差'
          }
        ]
      },
      {
        name: '概率分布',
        formulas: [
          {
            name: '正态分布',
            expression: 'f(x) = (1/σ√2π)e^(-½((x-μ)/σ)²)',
            description: '连续概率分布，呈钟形曲线'
          },
          {
            name: '二项分布',
            expression: 'P(X=k) = C(n,k)p^k(1-p)^(n-k)',
            description: 'n次独立试验中成功k次的概率'
          },
          {
            name: '泊松分布',
            expression: 'P(X=k) = (λ^k × e^(-λ)) / k!',
            description: '单位时间内随机事件发生次数的分布'
          },
          {
            name: '指数分布',
            expression: 'f(x) = λe^(-λx)',
            description: '描述事件间隔时间的概率分布'
          }
        ]
      },
      {
        name: '假设检验',
        formulas: [
          {
            name: 't检验统计量',
            expression: 't = (x̄ - μ) / (s / √n)',
            description: '用于小样本均值检验的统计量'
          },
          {
            name: 'Z检验统计量',
            expression: 'Z = (x̄ - μ) / (σ / √n)',
            description: '用于大样本均值检验的统计量'
          },
          {
            name: '卡方检验统计量',
            expression: 'χ² = Σ((O - E)² / E)',
            description: '用于拟合优度和独立性检验'
          },
          {
            name: 'F检验统计量',
            expression: 'F = s₁² / s₂²',
            description: '用于方差齐性检验'
          }
        ]
      },
      {
        name: '相关回归',
        formulas: [
          {
            name: '相关系数',
            expression: 'r = Σ(xy) / √(Σx² × Σy²)',
            description: '衡量两变量线性相关程度'
          },
          {
            name: '回归方程',
            expression: 'y = a + bx',
            description: '描述两变量间线性关系的方程'
          },
          {
            name: '回归系数',
            expression: 'b = Σ(xy) / Σx²',
            description: '回归直线的斜率'
          },
          {
            name: '决定系数',
            expression: 'R² = 1 - (SSE / SST)',
            description: '回归方程对数据的拟合程度'
          }
        ]
      }
    ]
  },

  onLoad() {
    this.loadUserData();
    this.checkAdStatus();
  },

  onShow() {
    this.checkAdStatus();
  },

  // 导航到计算页面
  navigateToCalculate(e) {
    const type = e.currentTarget.dataset.type;

    // 直接跳转到计算页面，跳过广告检查
    tt.navigateTo({
      url: `/pages/calculate/calculate?type=${type}`
    });
  },

  // 数据输入处理
  onDataInput(e) {
    const inputData = e.detail.value;
    this.setData({ inputData });
    
    // 解析数据
    this.parseInputData(inputData);
  },

  // 解析输入数据
  parseInputData(inputData) {
    if (!inputData.trim()) {
      this.setData({
        dataArray: [],
        previewText: '',
        quickStats: null
      });
      return;
    }
    
    // 支持逗号、空格、换行分隔
    const dataArray = inputData
      .replace(/[,，\s\n\r\t]+/g, ',')
      .split(',')
      .filter(item => item.trim() !== '')
      .map(item => {
        const num = parseFloat(item.trim());
        return isNaN(num) ? null : num;
      })
      .filter(item => item !== null);
    
    if (dataArray.length === 0) {
      this.setData({
        dataArray: [],
        previewText: '',
        quickStats: null
      });
      return;
    }
    
    // 生成预览文本
    const previewText = dataArray.length > 10 
      ? dataArray.slice(0, 10).join(', ') + '...'
      : dataArray.join(', ');
    
    this.setData({
      dataArray,
      previewText
    });
  },

  // 清空数据
  clearData() {
    this.setData({
      inputData: '',
      dataArray: [],
      previewText: '',
      quickStats: null
    });
  },

  // 粘贴数据
  pasteData() {
    tt.getClipboardData({
      success: (res) => {
        this.setData({ inputData: res.data });
        this.parseInputData(res.data);
      },
      fail: () => {
        tt.showToast({
          title: '粘贴失败',
          icon: 'none'
        });
      }
    });
  },

  // 快速分析
  quickAnalyze() {
    if (this.data.dataArray.length === 0) {
      tt.showToast({
        title: '请先输入数据',
        icon: 'none'
      });
      return;
    }
    
    const stats = this.calculateBasicStats(this.data.dataArray);
    this.setData({ quickStats: stats });
    
    // 记录使用统计
    this.updateUserStats('calculation');
    
    // 保存到历史记录
    this.saveToHistory('快速统计分析', `分析了${this.data.dataArray.length}个数据点`, stats);
  },

  // 计算基础统计量
  calculateBasicStats(data) {
    const n = data.length;
    const sum = data.reduce((a, b) => a + b, 0);
    const mean = sum / n;
    
    // 排序用于计算中位数
    const sorted = [...data].sort((a, b) => a - b);
    const median = n % 2 === 0 
      ? (sorted[n/2 - 1] + sorted[n/2]) / 2
      : sorted[Math.floor(n/2)];
    
    // 计算标准差
    const variance = data.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / n;
    const std = Math.sqrt(variance);
    
    const min = Math.min(...data);
    const max = Math.max(...data);
    
    return {
      count: n,
      mean: mean.toFixed(2),
      median: median.toFixed(2),
      std: std.toFixed(2),
      min: min.toFixed(2),
      max: max.toFixed(2)
    };
  },

  // 显示详细统计
  showDetailedStats() {
    // 直接跳转到详细统计页面，跳过广告检查
    tt.navigateTo({
      url: `/pages/calculate/calculate?type=descriptive&data=${JSON.stringify(this.data.dataArray)}`
    });
  },

  // 导出统计结果
  exportStats() {
    if (!this.data.quickStats) {
      tt.showToast({
        title: '暂无统计结果',
        icon: 'none'
      });
      return;
    }
    
    const statsText = `统计分析结果\n数据个数: ${this.data.quickStats.count}\n平均值: ${this.data.quickStats.mean}\n中位数: ${this.data.quickStats.median}\n标准差: ${this.data.quickStats.std}\n最小值: ${this.data.quickStats.min}\n最大值: ${this.data.quickStats.max}`;
    
    tt.setClipboardData({
      data: statsText,
      success: () => {
        tt.showToast({
          title: '结果已复制到剪贴板',
          icon: 'success'
        });
      }
    });
  },

  // 显示所有数据
  showAllData() {
    this.setData({ showDataModal: true });
  },

  // 隐藏数据弹窗
  hideDataModal() {
    this.setData({ showDataModal: false });
  },

  // 编辑数据
  editData() {
    this.setData({ showDataModal: false });
    // 可以添加数据编辑功能
  },

  // 分析数据
  analyzeData() {
    this.setData({ showDataModal: false });
    this.quickAnalyze();
  },

  // 使用公式
  useFormula(e) {
    const formula = e.currentTarget.dataset.formula;
    console.log('使用公式:', formula);

    // 直接跳转到公式计算页面，跳过广告检查
    tt.navigateTo({
      url: `/pages/calculate/calculate?type=formula&formula=${formula}`
    });
  },

  // 显示公式库
  showFormulaLibrary() {
    this.setData({ showFormulaModal: true });
  },

  // 隐藏公式库
  hideFormulaModal() {
    this.setData({ showFormulaModal: false });
  },

  // 选择公式
  selectFormula(e) {
    const formula = e.currentTarget.dataset.formula;
    this.setData({ showFormulaModal: false });

    // 直接跳转到公式计算页面，跳过广告检查
    tt.navigateTo({
      url: `/pages/calculate/calculate?type=formula&formula=${JSON.stringify(formula)}`
    });
  },

  // 显示历史记录
  showHistory() {
    this.setData({ showHistoryModal: true });
  },

  // 隐藏历史记录
  hideHistoryModal() {
    this.setData({ showHistoryModal: false });
  },

  // 使用历史记录项
  useHistoryItem(e) {
    const item = e.currentTarget.dataset.item;
    this.setData({ showHistoryModal: false });
    
    // 根据历史记录类型进行相应操作
    if (item.data) {
      this.setData({
        inputData: item.data.join(', '),
        dataArray: item.data
      });
      this.parseInputData(item.data.join(', '));
    }
  },

  // 删除历史记录项
  deleteHistoryItem(e) {
    e.stopPropagation();
    const id = e.currentTarget.dataset.id;
    const historyList = this.data.historyList.filter(item => item.id !== id);
    
    this.setData({ historyList });
    this.saveUserData();
  },

  // 清空历史记录
  clearHistory() {
    tt.showModal({
      title: '确认清空',
      content: '确定要清空所有计算历史吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({ historyList: [] });
          this.saveUserData();
          tt.showToast({
            title: '历史记录已清空',
            icon: 'success'
          });
        }
      }
    });
  },

  // 广告相关方法
  showRewardedAd() {
    this.setData({ showAdModal: true });
  },

  hideAdModal() {
    this.setData({ showAdModal: false });
  },

  watchAd() {
    tt.showLoading({ title: '加载广告中...' });
    
    setTimeout(() => {
      tt.hideLoading();
      this.setData({
        showAdModal: false,
        hasWatchedAd: true
      });
      
      tt.setStorageSync('adRewardTime', Date.now());
      
      tt.showToast({
        title: '广告观看完成，功能已解锁！',
        icon: 'success'
      });
    }, 2000);
  },

  checkAdStatus() {
    const adRewardTime = tt.getStorageSync('adRewardTime') || 0;
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;
    
    if (now - adRewardTime < oneHour) {
      this.setData({ hasWatchedAd: true });
    }
  },

  // 数据管理
  loadUserData() {
    try {
      const historyList = tt.getStorageSync('statisticsHistory') || [];
      const userStats = tt.getStorageSync('statisticsUserStats') || {
        totalCalculations: 0,
        totalDataPoints: 0,
        savedReports: 0
      };
      
      this.setData({
        historyList,
        userStats
      });
    } catch (error) {
      console.error('加载用户数据失败:', error);
    }
  },

  saveUserData() {
    try {
      tt.setStorageSync('statisticsHistory', this.data.historyList);
      tt.setStorageSync('statisticsUserStats', this.data.userStats);
    } catch (error) {
      console.error('保存用户数据失败:', error);
    }
  },

  // 保存到历史记录
  saveToHistory(type, description, result, data = null) {
    const historyList = this.data.historyList;
    const newItem = {
      id: Date.now(),
      type,
      description,
      result: typeof result === 'object' ? JSON.stringify(result) : result,
      data,
      time: new Date().toLocaleString()
    };
    
    historyList.unshift(newItem);
    if (historyList.length > 50) {
      historyList.pop();
    }
    
    this.setData({ historyList });
    this.saveUserData();
  },

  // 更新用户统计
  updateUserStats(type) {
    const userStats = { ...this.data.userStats };
    
    switch (type) {
      case 'calculation':
        userStats.totalCalculations++;
        userStats.totalDataPoints += this.data.dataArray.length;
        break;
      case 'report':
        userStats.savedReports++;
        break;
    }
    
    this.setData({ userStats });
    this.saveUserData();
    
    // 记录到全局统计
    app.recordCalculation();
  }
});
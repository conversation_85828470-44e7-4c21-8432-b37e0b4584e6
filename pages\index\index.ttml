<view class="container">
  <!-- 顶部标题 -->
  <view class="header">
    <text class="title">统计计算工具</text>
    <text class="subtitle">专业的数据分析与统计计算平台</text>
  </view>

  <!-- 功能分类卡片 -->
  <view class="function-categories">
    <view class="category-card" bindtap="navigateToCalculate" data-type="descriptive">
      <text class="card-icon">📊</text>
      <view class="card-content">
        <text class="card-title">描述统计</text>
        <text class="card-desc">计算均值、中位数、标准差、方差等基础统计量</text>
      </view>
      <text class="card-arrow">→</text>
      
    </view>

    <view class="category-card" bindtap="navigateToCalculate" data-type="probability">
      <text class="card-icon">🎲</text>
      <view class="card-content">
        <text class="card-title">概率计算</text>
        <text class="card-desc">正态分布、二项分布、泊松分布等概率计算</text>
      </view>
      <text class="card-arrow">→</text>
      
    </view>

    <view class="category-card" bindtap="navigateToCalculate" data-type="hypothesis">
      <text class="card-icon">🔬</text>
      <view class="card-content">
        <text class="card-title">假设检验</text>
        <text class="card-desc">t检验、卡方检验、方差分析等统计检验</text>
      </view>
      <text class="card-arrow">→</text>
      
    </view>

    <view class="category-card" bindtap="navigateToCalculate" data-type="regression">
      <text class="card-icon">📈</text>
      <view class="card-content">
        <text class="card-title">回归分析</text>
        <text class="card-desc">线性回归、多元回归、相关性分析</text>
      </view>
      <text class="card-arrow">→</text>
      
    </view>

    <view class="category-card" bindtap="navigateToCalculate" data-type="chart">
      <text class="card-icon">📉</text>
      <view class="card-content">
        <text class="card-title">图表分析</text>
        <text class="card-desc">直方图、散点图、箱线图等数据可视化</text>
      </view>
      <text class="card-arrow">→</text>
      
    </view>

    <view class="category-card" bindtap="navigateToCalculate" data-type="sampling">
      <text class="card-icon">🎯</text>
      <view class="card-content">
        <text class="card-title">抽样分析</text>
        <text class="card-desc">样本量计算、置信区间、抽样分布</text>
      </view>
      <text class="card-arrow">→</text>
      
    </view>
  </view>

 

  

  

  <!-- 计算历史 -->
  <view class="history-section" tt:if="{{historyList.length > 0}}">
    <view class="section-title">
      <text class="title-text">计算历史</text>
      <text class="title-action" bindtap="showHistory">查看全部</text>
    </view>
    
    <view class="history-list">
      <view class="history-item" tt:for="{{historyList.slice(0, 3)}}" tt:key="id" bindtap="useHistoryItem" data-item="{{item}}">
        <view class="history-content">
          <text class="history-title">{{item.type}}</text>
          <text class="history-desc">{{item.description}}</text>
        </view>
        <text class="history-time">{{item.time}}</text>
      </view>
    </view>
  </view>


  <!-- 广告弹窗 -->
  <view class="ad-modal" tt:if="{{showAdModal}}">
    <view class="ad-modal-mask" bindtap="hideAdModal"></view>
    <view class="ad-modal-content">
      <view class="ad-header">
        <text class="ad-title">解锁专业统计功能</text>
        <text class="ad-subtitle">观看短视频广告即可免费使用所有统计分析功能</text>
      </view>
      <view class="ad-benefits">
        <view class="benefit-item">
          <text class="benefit-icon">✓</text>
          <text class="benefit-text">高级统计分析算法</text>
        </view>
        <view class="benefit-item">
          <text class="benefit-icon">✓</text>
          <text class="benefit-text">专业图表生成</text>
        </view>
        <view class="benefit-item">
          <text class="benefit-icon">✓</text>
          <text class="benefit-text">假设检验和回归分析</text>
        </view>
        <view class="benefit-item">
          <text class="benefit-icon">✓</text>
          <text class="benefit-text">数据导出和报告生成</text>
        </view>
      </view>
      <view class="ad-buttons">
        <text class="btn-primary" bindtap="watchAd">
          <text class="btn-text">观看广告 (15秒)</text>
        </text>
        <text class="btn-secondary" bindtap="hideAdModal">
          <text class="btn-text">暂时跳过</text>
        </text>
      </view>
    </view>
  </view>

  <!-- 数据详情弹窗 -->
  <view class="data-modal" tt:if="{{showDataModal}}">
    <view class="data-modal-mask" bindtap="hideDataModal"></view>
    <view class="data-modal-content">
      <view class="data-header">
        <text class="data-title">完整数据</text>
        <text class="close-btn" bindtap="hideDataModal">×</text>
      </view>
      <scroll-view class="data-content" scroll-y="true">
        <view class="data-list">
          <view class="data-row" tt:for="{{dataArray}}" tt:key="index">
            <text class="data-index">{{index + 1}}.</text>
            <text class="data-value">{{item}}</text>
          </view>
        </view>
      </scroll-view>
      <view class="data-footer">
        <text class="btn btn-secondary" bindtap="editData">编辑数据</text>
        <text class="btn btn-primary" bindtap="analyzeData">开始分析</text>
      </view>
    </view>
  </view>

  <!-- 公式库弹窗 -->
  <view class="formula-modal" tt:if="{{showFormulaModal}}">
    <view class="formula-modal-mask" bindtap="hideFormulaModal"></view>
    <view class="formula-modal-content">
      <view class="formula-header">
        <text class="formula-title">统计公式库</text>
        <text class="close-btn" bindtap="hideFormulaModal">×</text>
      </view>
      <scroll-view class="formula-content" scroll-y="true">
        <view class="formula-category" tt:for="{{formulaCategories}}" tt:key="name">
          <text class="category-title">{{item.name}}</text>
          <view class="category-formulas">
            <view class="formula-detail" tt:for="{{item.formulas}}" tt:key="name" tt:for-item="formula" bindtap="selectFormula" data-formula="{{formula}}">
              <text class="formula-name">{{formula.name}}</text>
              <text class="formula-expression">{{formula.expression}}</text>
              <text class="formula-description">{{formula.description}}</text>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 历史记录弹窗 -->
  <view class="history-modal" tt:if="{{showHistoryModal}}">
    <view class="history-modal-mask" bindtap="hideHistoryModal"></view>
    <view class="history-modal-content">
      <view class="history-header">
        <text class="history-title">计算历史</text>
        <view class="history-actions">
          <text class="clear-history-btn" bindtap="clearHistory">清空</text>
          <text class="close-btn" bindtap="hideHistoryModal">×</text>
        </view>
      </view>
      <scroll-view class="history-content" scroll-y="true">
        <view class="history-list" tt:if="{{historyList.length > 0}}">
          <view class="history-item-full" tt:for="{{historyList}}" tt:key="id" bindtap="useHistoryItem" data-item="{{item}}">
            <view class="history-content-full">
              <text class="history-type-full">{{item.type}}</text>
              <text class="history-desc-full">{{item.description}}</text>
              <text class="history-result-full">{{item.result}}</text>
            </view>
            <view class="history-meta">
              <text class="history-time-full">{{item.time}}</text>
              <text class="history-delete" bindtap="deleteHistoryItem" data-id="{{item.id}}">🗑</text>
            </view>
          </view>
        </view>
        <view class="empty-history" tt:else>
          <text class="empty-icon">📊</text>
          <text class="empty-title">暂无计算历史</text>
          <text class="empty-description">开始统计分析后，历史记录将显示在这里</text>
        </view>
      </scroll-view>
    </view>
  </view>
</view>
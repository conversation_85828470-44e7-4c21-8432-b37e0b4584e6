.container {
  min-height: 100vh;
  padding: 20px;
  background: linear-gradient(135deg, #F8FAFC 0%, #E2E8F0 100%);
}

/* 顶部标题 */
.header {
  text-align: center;
  margin-bottom: 32px;
  padding: 20px 0;
}

.title {
  font-size: 28px;
  font-weight: bold;
  color: #1E293B;
  display: block;
  margin-bottom: 8px;
}

.subtitle {
  font-size: 16px;
  color: #64748B;
  display: block;
}

/* 功能分类卡片 */
.function-categories {
  margin-bottom: 32px;
}

.category-card {
  display: flex;
  align-items: center;
  padding: 20px;
  margin-bottom: 16px;
  background: #FFFFFF;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.category-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.5s;
}

.category-card:active {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.category-card:active::before {
  left: 100%;
}

.card-icon {
  font-size: 32px;
  margin-right: 16px;
  width: 48px;
  text-align: center;
}

.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  color: #1E293B;
  margin-bottom: 4px;
  display: block;
}

.card-desc {
  font-size: 14px;
  color: #64748B;
  line-height: 1.4;
  display: block;
}

.card-arrow {
  font-size: 20px;
  color: #3B82F6;
  margin-left: 12px;
}

.premium-tag {
  position: absolute;
  top: 12px;
  right: 12px;
  background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
  color: #FFFFFF;
  font-size: 12px;
  font-weight: bold;
  padding: 4px 8px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

/* 快速数据输入 */
.quick-input-section {
  margin-bottom: 32px;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 4px;
}

.title-text {
  font-size: 20px;
  font-weight: bold;
  color: #1E293B;
}

.title-desc {
  font-size: 14px;
  color: #64748B;
}

.title-action {
  font-size: 14px;
  color: #3B82F6;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 4px;
  background: rgba(59, 130, 246, 0.1);
  transition: all 0.3s ease;
}

.title-action:active {
  background: rgba(59, 130, 246, 0.2);
  transform: scale(0.95);
}

.input-area {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  margin-bottom: 16px;
}

.data-input {
  width: 100%;
  min-height: 100px;
  padding: 16px;
  border: 1px solid #E2E8F0;
  border-radius: 8px;
  font-size: 16px;
  color: #1E293B;
  background: #F8FAFC;
  resize: none;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  line-height: 1.5;
  margin-bottom: 16px;
}

.data-input:focus {
  border-color: #3B82F6;
  outline: none;
  background: #FFFFFF;
}

.input-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.action-btn {
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid #E2E8F0;
  background: #F8FAFC;
  color: #64748B;
}

.action-btn.primary {
  background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
  color: #FFFFFF;
  border-color: #3B82F6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.action-btn:active {
  transform: scale(0.95);
}

/* 数据预览 */
.data-preview {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 16px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.preview-title {
  font-size: 14px;
  font-weight: 600;
  color: #1E293B;
}

.preview-action {
  font-size: 12px;
  color: #3B82F6;
  padding: 4px 8px;
  border-radius: 4px;
  background: rgba(59, 130, 246, 0.1);
  transition: all 0.3s ease;
}

.preview-action:active {
  background: rgba(59, 130, 246, 0.2);
  transform: scale(0.95);
}

.preview-content {
  background: #F8FAFC;
  border-radius: 6px;
  padding: 12px;
}

.preview-data {
  font-size: 14px;
  color: #374151;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  line-height: 1.4;
  word-break: break-all;
}

/* 快速统计结果 */
.quick-stats {
  margin-bottom: 32px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 20px;
}

.stats-item {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 20px 16px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stats-number {
  font-size: 24px;
  font-weight: bold;
  color: #3B82F6;
  margin-bottom: 4px;
  display: block;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.stats-label {
  font-size: 12px;
  color: #64748B;
  display: block;
  text-align: center;
}

.stats-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

/* 常用统计公式 */
.common-formulas {
  margin-bottom: 32px;
}

.formula-scroll {
  white-space: nowrap;
}

.formula-list {
  display: flex;
  gap: 16px;
  padding: 4px 0;
}

.formula-item {
  min-width: 160px;
  background: #FFFFFF;
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.formula-item:active {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.formula-icon {
  font-size: 24px;
  margin-bottom: 8px;
  color: #3B82F6;
  font-weight: bold;
}

.formula-name {
  font-size: 14px;
  font-weight: 600;
  color: #1E293B;
  margin-bottom: 4px;
  display: block;
}

.formula-expression {
  font-size: 12px;
  color: #64748B;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  display: block;
}

/* 计算历史 */
.history-section {
  margin-bottom: 32px;
}

.history-list {
  background: #FFFFFF;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.history-item {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #F1F5F9;
  transition: background 0.2s ease;
}

.history-item:last-child {
  border-bottom: none;
}

.history-item:active {
  background: #F8FAFC;
}

.history-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.history-title {
  font-size: 16px;
  color: #1E293B;
  font-weight: 600;
  margin-bottom: 4px;
  display: block;
}

.history-desc {
  font-size: 14px;
  color: #64748B;
  display: block;
}

.history-time {
  font-size: 12px;
  color: #9CA3AF;
  margin-left: 12px;
}

/* 使用统计 */
.statistics-section {
  margin-bottom: 32px;
}

/* 广告横幅 */
.ad-banner {
  text-align: center;
  padding: 16px;
  background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
  margin-bottom: 32px;
}

.ad-banner:active {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.ad-text {
  color: #FFFFFF;
  font-size: 16px;
  font-weight: 500;
}

/* 弹窗样式 */
.ad-modal, .data-modal, .formula-modal, .history-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.ad-modal-content, .data-modal-content, .formula-modal-content, .history-modal-content {
  background: #FFFFFF;
  border-radius: 16px;
  margin: 20px;
  max-width: 400px;
  width: 100%;
  max-height: 70vh;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.ad-modal-mask, .data-modal-mask, .formula-modal-mask, .history-modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

/* 广告弹窗 */
.ad-modal-content {
  max-width: 320px;
}

.ad-header {
  text-align: center;
  padding: 24px 24px 0 24px;
  margin-bottom: 24px;
}

.ad-title {
  font-size: 20px;
  font-weight: bold;
  color: #1E293B;
  margin-bottom: 8px;
  display: block;
}

.ad-subtitle {
  font-size: 14px;
  color: #64748B;
  line-height: 1.5;
  display: block;
}

.ad-benefits {
  padding: 0 24px;
  margin-bottom: 24px;
}

.benefit-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  text-align: left;
}

.benefit-item:last-child {
  margin-bottom: 0;
}

.benefit-icon {
  font-size: 16px;
  margin-right: 12px;
  color: #10B981;
}

.benefit-text {
  font-size: 14px;
  color: #374151;
}

.ad-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 0 24px 24px 24px;
}

.btn-primary, .btn-secondary {
  padding: 12px 24px;
  border-radius: 8px;
  text-align: center;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
  color: #FFFFFF;
}

.btn-secondary {
  background: #F1F5F9;
  color: #64748B;
}

.btn-primary:active, .btn-secondary:active {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-text {
  font-size: 16px;
}

/* 数据详情弹窗 */
.data-modal-content {
  max-width: 500px;
  display: flex;
  flex-direction: column;
}

.data-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 0 20px;
  border-bottom: 1px solid #E2E8F0;
}

.data-title {
  font-size: 18px;
  font-weight: bold;
  color: #1E293B;
}

.close-btn {
  font-size: 24px;
  color: #64748B;
  padding: 4px;
  border-radius: 4px;
  background: #F1F5F9;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-btn:active {
  background: #E2E8F0;
  transform: scale(0.95);
}

.data-content {
  flex: 1;
  padding: 20px;
  max-height: 300px;
}

.data-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.data-row {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #F8FAFC;
  border-radius: 6px;
}

.data-index {
  font-size: 14px;
  color: #64748B;
  margin-right: 12px;
  min-width: 30px;
}

.data-value {
  font-size: 14px;
  color: #1E293B;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.data-footer {
  display: flex;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #E2E8F0;
}

/* 公式库弹窗 */
.formula-modal-content {
  max-width: 500px;
  display: flex;
  flex-direction: column;
}

.formula-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 0 20px;
  border-bottom: 1px solid #E2E8F0;
}

.formula-title {
  font-size: 18px;
  font-weight: bold;
  color: #1E293B;
}

.formula-content {
  flex: 1;
  padding: 20px;
  max-height: 400px;
}

.formula-category {
  margin-bottom: 24px;
}

.formula-category:last-child {
  margin-bottom: 0;
}

.category-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
  display: block;
}

.category-formulas {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.formula-detail {
  background: #F8FAFC;
  border: 1px solid #E2E8F0;
  border-radius: 8px;
  padding: 12px;
  transition: background 0.2s ease;
}

.formula-detail:active {
  background: #F1F5F9;
}

.formula-detail .formula-name {
  font-size: 14px;
  font-weight: 600;
  color: #1E293B;
  margin-bottom: 4px;
  display: block;
}

.formula-detail .formula-expression {
  font-size: 13px;
  color: #3B82F6;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  margin-bottom: 4px;
  display: block;
}

.formula-description {
  font-size: 12px;
  color: #64748B;
  line-height: 1.4;
  display: block;
}

/* 历史记录弹窗 */
.history-modal-content {
  display: flex;
  flex-direction: column;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 0 20px;
  border-bottom: 1px solid #E2E8F0;
}

.history-title {
  font-size: 18px;
  font-weight: bold;
  color: #1E293B;
}

.history-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.clear-history-btn {
  color: #EF4444;
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 4px;
  background: rgba(239, 68, 68, 0.1);
  transition: all 0.3s ease;
}

.clear-history-btn:active {
  background: rgba(239, 68, 68, 0.2);
  transform: scale(0.95);
}

.history-content {
  flex: 1;
  padding: 20px;
  max-height: 400px;
}

.history-item-full {
  display: flex;
  align-items: center;
  background: #F8FAFC;
  border: 1px solid #E2E8F0;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
  transition: background 0.2s ease;
}

.history-item-full:last-child {
  margin-bottom: 0;
}

.history-item-full:active {
  background: #F1F5F9;
}

.history-content-full {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.history-type-full {
  font-size: 14px;
  color: #1E293B;
  font-weight: 600;
  margin-bottom: 4px;
  display: block;
}

.history-desc-full {
  font-size: 13px;
  color: #64748B;
  margin-bottom: 4px;
  display: block;
}

.history-result-full {
  font-size: 12px;
  color: #3B82F6;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  display: block;
}

.history-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.history-time-full {
  font-size: 11px;
  color: #9CA3AF;
}

.history-delete {
  font-size: 16px;
  color: #EF4444;
  padding: 4px;
  border-radius: 4px;
  background: rgba(239, 68, 68, 0.1);
  transition: all 0.3s ease;
}

.history-delete:active {
  background: rgba(239, 68, 68, 0.2);
  transform: scale(0.95);
}

.empty-history {
  text-align: center;
  padding: 40px 20px;
  color: #9CA3AF;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
  display: block;
}

.empty-title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #6B7280;
  display: block;
}

.empty-description {
  font-size: 14px;
  line-height: 1.5;
  display: block;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .container {
    padding: 16px;
  }
  
  .title {
    font-size: 24px;
  }
  
  .category-card {
    padding: 16px;
  }
  
  .card-icon {
    font-size: 28px;
    width: 40px;
  }
  
  .card-title {
    font-size: 16px;
  }
  
  .data-input {
    min-height: 80px;
    padding: 12px;
    font-size: 14px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .stats-number {
    font-size: 20px;
  }
  
  .formula-list {
    gap: 12px;
  }
  
  .formula-item {
    min-width: 140px;
    padding: 12px;
  }
  
  .ad-modal-content, .data-modal-content, .formula-modal-content, .history-modal-content {
    margin: 16px;
  }
}

/* 滚动条样式 */
.data-content::-webkit-scrollbar,
.formula-content::-webkit-scrollbar,
.history-content::-webkit-scrollbar {
  width: 4px;
}

.data-content::-webkit-scrollbar-track,
.formula-content::-webkit-scrollbar-track,
.history-content::-webkit-scrollbar-track {
  background: #F1F5F9;
  border-radius: 2px;
}

.data-content::-webkit-scrollbar-thumb,
.formula-content::-webkit-scrollbar-thumb,
.history-content::-webkit-scrollbar-thumb {
  background: #CBD5E1;
  border-radius: 2px;
}

.data-content::-webkit-scrollbar-thumb:hover,
.formula-content::-webkit-scrollbar-thumb:hover,
.history-content::-webkit-scrollbar-thumb:hover {
  background: #94A3B8;
}
